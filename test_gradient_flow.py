#!/usr/bin/env python3
"""
测试梯度传播链条的完整性
验证从网络输出到监督损失的梯度连接
"""

import torch
import numpy as np
import sys
import os

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from env.ieee30_env import IEEE30Env
from agents.gnn.gnn_ddpg import GNNDDPGAgent
from utils.graph_utils import state_to_graph

def test_gradient_flow():
    """测试完整的梯度传播链条"""
    print("=== 测试梯度传播链条 ===")
    
    # 1. 初始化环境和智能体
    print("1. 初始化环境和智能体...")
    env = IEEE30Env()
    state = env.reset()
    
    # 创建智能体（简化配置）
    agent_config = {
        'state_dim': len(state),
        'action_dim': 6,  # 5个受控发电机 + 1个风电
        'hidden_dim': 64,
        'lr_actor': 1e-4,
        'lr_critic': 1e-3,
        'batch_size': 32,
        'buffer_size': 10000,
        'time_steps': 4
    }
    
    agent = GNNDDPGAgent(**agent_config)
    
    # 2. 测试网络输出是否有梯度
    print("\n2. 测试网络输出梯度...")
    graph_list = state_to_graph(state, env.graph_builder, time_steps=4, node_feature_dim=len(state)//4)
    
    # 网络前向传播
    action = agent.select_action(state, env.graph_builder, add_noise=False)
    print(f"网络输出action: {action}")
    print(f"action.requires_grad: {action.requires_grad}")
    print(f"action.grad_fn: {action.grad_fn}")
    
    if not action.requires_grad:
        print("❌ 错误：网络输出没有梯度！")
        return False
    else:
        print("✅ 网络输出有梯度")
    
    # 3. 测试环境处理后是否保留梯度
    print("\n3. 测试环境处理梯度保留...")
    next_state, reward, done, info = env.step(action)
    adjusted_action = info.get('adjusted_action', action)
    
    print(f"调节后动作: {adjusted_action}")
    print(f"adjusted_action类型: {type(adjusted_action)}")
    
    if isinstance(adjusted_action, torch.Tensor):
        print(f"adjusted_action.requires_grad: {adjusted_action.requires_grad}")
        print(f"adjusted_action.grad_fn: {adjusted_action.grad_fn}")
        
        if not adjusted_action.requires_grad:
            print("❌ 错误：调节后动作没有梯度！")
            return False
        else:
            print("✅ 调节后动作有梯度")
    else:
        print("❌ 错误：调节后动作不是PyTorch张量！")
        return False
    
    # 4. 测试监督损失计算
    print("\n4. 测试监督损失计算...")
    
    # 创建模拟的CSV目标值
    csv_target = torch.randn_like(adjusted_action)
    print(f"CSV目标值: {csv_target}")
    
    # 计算监督损失
    supervision_loss = torch.nn.functional.mse_loss(adjusted_action, csv_target)
    print(f"监督损失: {supervision_loss}")
    print(f"supervision_loss.requires_grad: {supervision_loss.requires_grad}")
    print(f"supervision_loss.grad_fn: {supervision_loss.grad_fn}")
    
    if not supervision_loss.requires_grad:
        print("❌ 错误：监督损失没有梯度！")
        return False
    else:
        print("✅ 监督损失有梯度")
    
    # 5. 测试反向传播
    print("\n5. 测试反向传播...")
    
    # 清零梯度
    agent.actor_optimizer.zero_grad()
    
    # 获取网络参数的初始梯度状态
    initial_grads = []
    for param in agent.actor.parameters():
        if param.grad is not None:
            initial_grads.append(param.grad.clone())
        else:
            initial_grads.append(None)
    
    try:
        # 反向传播
        supervision_loss.backward()
        print("✅ 反向传播成功")
        
        # 检查梯度是否更新
        grad_updated = False
        for i, param in enumerate(agent.actor.parameters()):
            if param.grad is not None:
                if initial_grads[i] is None or not torch.equal(param.grad, initial_grads[i]):
                    grad_updated = True
                    break
        
        if grad_updated:
            print("✅ 网络参数梯度已更新")
        else:
            print("❌ 警告：网络参数梯度未更新")
            
    except Exception as e:
        print(f"❌ 错误：反向传播失败 - {e}")
        return False
    
    # 6. 完整性测试总结
    print("\n=== 梯度传播链条测试结果 ===")
    print("✅ 网络输出 → 有梯度")
    print("✅ 环境处理 → 保留梯度") 
    print("✅ 调节后动作 → 有梯度")
    print("✅ 监督损失 → 有梯度")
    print("✅ 反向传播 → 成功")
    print("✅ 梯度传播链条完整！")
    
    return True

def test_batch_gradient_flow():
    """测试批量数据的梯度传播"""
    print("\n=== 测试批量梯度传播 ===")
    
    # 创建模拟的批量数据
    batch_size = 4
    action_dim = 6
    
    # 模拟有梯度的调节后动作
    adjusted_actions = torch.randn(batch_size, action_dim, requires_grad=True)
    csv_targets = torch.randn(batch_size, action_dim)
    
    print(f"批量调节后动作形状: {adjusted_actions.shape}")
    print(f"批量CSV目标形状: {csv_targets.shape}")
    print(f"adjusted_actions.requires_grad: {adjusted_actions.requires_grad}")
    
    # 计算批量监督损失
    batch_supervision_loss = torch.nn.functional.mse_loss(adjusted_actions, csv_targets)
    print(f"批量监督损失: {batch_supervision_loss}")
    print(f"batch_supervision_loss.requires_grad: {batch_supervision_loss.requires_grad}")
    
    # 测试批量反向传播
    try:
        batch_supervision_loss.backward()
        print("✅ 批量反向传播成功")
        print(f"adjusted_actions梯度形状: {adjusted_actions.grad.shape}")
        print("✅ 批量梯度传播测试通过")
        return True
    except Exception as e:
        print(f"❌ 批量反向传播失败: {e}")
        return False

if __name__ == "__main__":
    print("开始测试梯度传播链条...")
    
    # 测试单步梯度流
    success1 = test_gradient_flow()
    
    # 测试批量梯度流
    success2 = test_batch_gradient_flow()
    
    if success1 and success2:
        print("\n🎉 所有梯度传播测试通过！监督学习应该能正常工作。")
    else:
        print("\n❌ 梯度传播测试失败，需要进一步调试。")
